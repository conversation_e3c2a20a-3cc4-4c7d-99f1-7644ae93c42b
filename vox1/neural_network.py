"""
Neural network module for VoX-1 Voice ID Engine
Implements voice identification neural network
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from typing import Tuple, Optional, List
import logging
import os

logger = logging.getLogger(__name__)

class VoiceNet:
    """Neural network for voice identification and verification."""
    
    def __init__(self, 
                 input_dim: int = 136,  # Default feature dimension
                 embedding_dim: int = 128,
                 hidden_dims: List[int] = [256, 128],
                 dropout_rate: float = 0.3,
                 learning_rate: float = 0.001):
        """
        Initialize VoiceNet.
        
        Args:
            input_dim: Input feature dimension
            embedding_dim: Voice embedding dimension
            hidden_dims: Hidden layer dimensions
            dropout_rate: Dropout rate for regularization
            learning_rate: Learning rate for optimizer
        """
        self.input_dim = input_dim
        self.embedding_dim = embedding_dim
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        
        self.model = None
        self.encoder = None
        self.is_trained = False
        
        logger.info(f"VoiceNet initialized with input_dim={input_dim}, embedding_dim={embedding_dim}")
    
    def build_model(self, num_speakers: int) -> None:
        """
        Build the neural network model.
        
        Args:
            num_speakers: Number of speakers for classification
        """
        # Input layer
        inputs = keras.Input(shape=(self.input_dim,), name='voice_features')
        
        # Feature extraction layers
        x = inputs
        for i, hidden_dim in enumerate(self.hidden_dims):
            x = layers.Dense(
                hidden_dim, 
                activation='relu',
                name=f'dense_{i+1}'
            )(x)
            x = layers.BatchNormalization(name=f'bn_{i+1}')(x)
            x = layers.Dropout(self.dropout_rate, name=f'dropout_{i+1}')(x)
        
        # Voice embedding layer
        embedding = layers.Dense(
            self.embedding_dim,
            activation='relu',
            name='voice_embedding'
        )(x)
        
        # L2 normalization for embedding
        embedding_normalized = layers.Lambda(
            lambda x: tf.nn.l2_normalize(x, axis=1),
            name='embedding_normalized'
        )(embedding)
        
        # Classification head
        outputs = layers.Dense(
            num_speakers,
            activation='softmax',
            name='speaker_classification'
        )(embedding_normalized)
        
        # Create models
        self.model = keras.Model(inputs=inputs, outputs=outputs, name='VoiceNet')
        self.encoder = keras.Model(inputs=inputs, outputs=embedding_normalized, name='VoiceEncoder')
        
        # Compile model
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=self.learning_rate),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        logger.info(f"Model built for {num_speakers} speakers")
        logger.info(f"Model summary:\n{self.model.summary()}")
    
    def train(self, 
              X_train: np.ndarray, 
              y_train: np.ndarray,
              X_val: Optional[np.ndarray] = None,
              y_val: Optional[np.ndarray] = None,
              epochs: int = 100,
              batch_size: int = 32,
              early_stopping_patience: int = 10) -> keras.callbacks.History:
        """
        Train the voice identification model.
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features
            y_val: Validation labels
            epochs: Number of training epochs
            batch_size: Batch size
            early_stopping_patience: Early stopping patience
            
        Returns:
            Training history
        """
        if self.model is None:
            num_speakers = len(np.unique(y_train))
            self.build_model(num_speakers)
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss' if X_val is not None else 'loss',
                patience=early_stopping_patience,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss' if X_val is not None else 'loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # Validation data
        validation_data = None
        if X_val is not None and y_val is not None:
            validation_data = (X_val, y_val)
        
        logger.info(f"Starting training for {epochs} epochs...")
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            validation_data=validation_data,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        self.is_trained = True
        logger.info("Training completed")
        
        return history
    
    def predict_speaker(self, features: np.ndarray) -> Tuple[int, float]:
        """
        Predict speaker from voice features.
        
        Args:
            features: Voice features
            
        Returns:
            Tuple of (predicted_speaker_id, confidence)
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before prediction")
        
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        predictions = self.model.predict(features, verbose=0)
        
        speaker_id = np.argmax(predictions[0])
        confidence = float(predictions[0][speaker_id])
        
        return speaker_id, confidence
    
    def get_voice_embedding(self, features: np.ndarray) -> np.ndarray:
        """
        Get voice embedding from features.
        
        Args:
            features: Voice features
            
        Returns:
            Voice embedding vector
        """
        if self.encoder is None:
            raise ValueError("Model must be built before extracting embeddings")
        
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        embedding = self.encoder.predict(features, verbose=0)
        return embedding[0] if len(embedding) == 1 else embedding
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Compute cosine similarity between two voice embeddings.
        
        Args:
            embedding1: First voice embedding
            embedding2: Second voice embedding
            
        Returns:
            Cosine similarity score
        """
        # Normalize embeddings
        embedding1_norm = embedding1 / np.linalg.norm(embedding1)
        embedding2_norm = embedding2 / np.linalg.norm(embedding2)
        
        # Compute cosine similarity
        similarity = np.dot(embedding1_norm, embedding2_norm)
        return float(similarity)
    
    def save_model(self, model_path: str) -> None:
        """
        Save the trained model.
        
        Args:
            model_path: Path to save the model
        """
        if self.model is None:
            raise ValueError("No model to save")
        
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        self.model.save(model_path)
        
        # Save encoder separately
        encoder_path = model_path.replace('.h5', '_encoder.h5')
        if self.encoder is not None:
            self.encoder.save(encoder_path)
        
        logger.info(f"Model saved to {model_path}")
    
    def load_model(self, model_path: str) -> None:
        """
        Load a trained model.
        
        Args:
            model_path: Path to the saved model
        """
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        self.model = keras.models.load_model(model_path)
        
        # Try to load encoder
        encoder_path = model_path.replace('.h5', '_encoder.h5')
        if os.path.exists(encoder_path):
            self.encoder = keras.models.load_model(encoder_path)
        else:
            # Create encoder from loaded model
            self.encoder = keras.Model(
                inputs=self.model.input,
                outputs=self.model.get_layer('embedding_normalized').output
            )
        
        self.is_trained = True
        logger.info(f"Model loaded from {model_path}")
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> dict:
        """
        Evaluate model performance.
        
        Args:
            X_test: Test features
            y_test: Test labels
            
        Returns:
            Dictionary with evaluation metrics
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before evaluation")
        
        # Get predictions
        predictions = self.model.predict(X_test, verbose=0)
        predicted_labels = np.argmax(predictions, axis=1)
        
        # Calculate metrics
        accuracy = np.mean(predicted_labels == y_test)
        
        # Per-class accuracy
        unique_labels = np.unique(y_test)
        per_class_accuracy = {}
        for label in unique_labels:
            mask = y_test == label
            if np.sum(mask) > 0:
                per_class_accuracy[int(label)] = np.mean(predicted_labels[mask] == label)
        
        results = {
            'accuracy': float(accuracy),
            'per_class_accuracy': per_class_accuracy,
            'num_samples': len(y_test),
            'num_classes': len(unique_labels)
        }
        
        logger.info(f"Evaluation results: {results}")
        return results
