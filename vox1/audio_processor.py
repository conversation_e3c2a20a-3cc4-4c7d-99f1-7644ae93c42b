"""
Audio processing module for VoX-1 Voice ID Engine
Handles audio preprocessing and feature extraction
"""

import numpy as np
import librosa
import soundfile as sf
from typing import Tuple, Optional, Union
import logging
from .utils import normalize_audio, apply_voice_activity_detection

logger = logging.getLogger(__name__)

class AudioProcessor:
    """Audio preprocessing and feature extraction for voice identification."""
    
    def __init__(self, 
                 sample_rate: int = 16000,
                 n_mfcc: int = 13,
                 n_fft: int = 2048,
                 hop_length: int = 512,
                 n_mels: int = 128,
                 fmin: float = 0.0,
                 fmax: Optional[float] = None):
        """
        Initialize AudioProcessor.
        
        Args:
            sample_rate: Target sample rate for audio
            n_mfcc: Number of MFCC coefficients
            n_fft: FFT window size
            hop_length: Hop length for STFT
            n_mels: Number of mel bands
            fmin: Minimum frequency
            fmax: Maximum frequency (None for sr/2)
        """
        self.sample_rate = sample_rate
        self.n_mfcc = n_mfcc
        self.n_fft = n_fft
        self.hop_length = hop_length
        self.n_mels = n_mels
        self.fmin = fmin
        self.fmax = fmax or sample_rate // 2
        
        logger.info(f"AudioProcessor initialized with sr={sample_rate}, n_mfcc={n_mfcc}")
    
    def load_audio(self, file_path: str) -> Tuple[np.ndarray, int]:
        """
        Load audio file and resample to target sample rate.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Tuple of (audio_data, sample_rate)
        """
        try:
            audio, sr = librosa.load(file_path, sr=self.sample_rate)
            logger.debug(f"Loaded audio: {file_path}, duration: {len(audio)/sr:.2f}s")
            return audio, sr
        except Exception as e:
            logger.error(f"Error loading audio file {file_path}: {e}")
            raise
    
    def preprocess_audio(self, audio: np.ndarray, 
                        apply_vad: bool = True,
                        normalize: bool = True) -> np.ndarray:
        """
        Preprocess audio signal.
        
        Args:
            audio: Raw audio signal
            apply_vad: Whether to apply voice activity detection
            normalize: Whether to normalize audio
            
        Returns:
            Preprocessed audio signal
        """
        processed_audio = audio.copy()
        
        # Normalize audio
        if normalize:
            processed_audio = normalize_audio(processed_audio)
        
        # Apply voice activity detection
        if apply_vad:
            processed_audio = apply_voice_activity_detection(
                processed_audio, self.sample_rate
            )
        
        # Pre-emphasis filter
        processed_audio = self._apply_preemphasis(processed_audio)
        
        return processed_audio
    
    def extract_mfcc_features(self, audio: np.ndarray) -> np.ndarray:
        """
        Extract MFCC features from audio.
        
        Args:
            audio: Preprocessed audio signal
            
        Returns:
            MFCC feature matrix (n_mfcc, n_frames)
        """
        try:
            mfcc = librosa.feature.mfcc(
                y=audio,
                sr=self.sample_rate,
                n_mfcc=self.n_mfcc,
                n_fft=self.n_fft,
                hop_length=self.hop_length,
                fmin=self.fmin,
                fmax=self.fmax
            )
            
            # Add delta and delta-delta features
            delta_mfcc = librosa.feature.delta(mfcc)
            delta2_mfcc = librosa.feature.delta(mfcc, order=2)
            
            # Combine features
            features = np.vstack([mfcc, delta_mfcc, delta2_mfcc])
            
            logger.debug(f"Extracted MFCC features: {features.shape}")
            return features
            
        except Exception as e:
            logger.error(f"Error extracting MFCC features: {e}")
            raise
    
    def extract_spectral_features(self, audio: np.ndarray) -> np.ndarray:
        """
        Extract spectral features from audio.
        
        Args:
            audio: Preprocessed audio signal
            
        Returns:
            Spectral feature vector
        """
        try:
            # Spectral centroid
            spectral_centroids = librosa.feature.spectral_centroid(
                y=audio, sr=self.sample_rate, hop_length=self.hop_length
            )[0]
            
            # Spectral rolloff
            spectral_rolloff = librosa.feature.spectral_rolloff(
                y=audio, sr=self.sample_rate, hop_length=self.hop_length
            )[0]
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(
                audio, hop_length=self.hop_length
            )[0]
            
            # Spectral bandwidth
            spectral_bandwidth = librosa.feature.spectral_bandwidth(
                y=audio, sr=self.sample_rate, hop_length=self.hop_length
            )[0]
            
            # Combine features
            features = np.vstack([
                spectral_centroids,
                spectral_rolloff,
                zcr,
                spectral_bandwidth
            ])
            
            logger.debug(f"Extracted spectral features: {features.shape}")
            return features
            
        except Exception as e:
            logger.error(f"Error extracting spectral features: {e}")
            raise
    
    def extract_voice_features(self, audio: np.ndarray) -> np.ndarray:
        """
        Extract comprehensive voice features.
        
        Args:
            audio: Raw audio signal
            
        Returns:
            Combined feature vector
        """
        # Preprocess audio
        processed_audio = self.preprocess_audio(audio)
        
        if len(processed_audio) == 0:
            logger.warning("No voice activity detected in audio")
            return np.array([])
        
        # Extract MFCC features
        mfcc_features = self.extract_mfcc_features(processed_audio)
        
        # Extract spectral features
        spectral_features = self.extract_spectral_features(processed_audio)
        
        # Combine all features
        combined_features = np.vstack([mfcc_features, spectral_features])
        
        # Statistical features (mean, std, min, max)
        feature_stats = self._compute_feature_statistics(combined_features)
        
        logger.debug(f"Final feature vector shape: {feature_stats.shape}")
        return feature_stats
    
    def _apply_preemphasis(self, audio: np.ndarray, alpha: float = 0.97) -> np.ndarray:
        """Apply pre-emphasis filter to audio."""
        return np.append(audio[0], audio[1:] - alpha * audio[:-1])
    
    def _compute_feature_statistics(self, features: np.ndarray) -> np.ndarray:
        """Compute statistical features from time-series features."""
        if features.size == 0:
            return np.array([])
        
        # Compute statistics along time axis
        mean_features = np.mean(features, axis=1)
        std_features = np.std(features, axis=1)
        min_features = np.min(features, axis=1)
        max_features = np.max(features, axis=1)
        
        # Combine statistics
        stats = np.concatenate([mean_features, std_features, min_features, max_features])
        
        return stats
