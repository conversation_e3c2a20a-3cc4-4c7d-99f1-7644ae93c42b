"""
Voice database module for VoX-1 Voice ID Engine
Manages voice profiles and speaker data
"""

import os
import json
import numpy as np
import pickle
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime
from .utils import ensure_directory

logger = logging.getLogger(__name__)

class VoiceProfile:
    """Represents a voice profile for a speaker."""
    
    def __init__(self, 
                 speaker_id: int,
                 speaker_name: str,
                 voice_embedding: np.ndarray,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize voice profile.
        
        Args:
            speaker_id: Unique speaker identifier
            speaker_name: Speaker name
            voice_embedding: Voice embedding vector
            metadata: Additional metadata
        """
        self.speaker_id = speaker_id
        self.speaker_name = speaker_name
        self.voice_embedding = voice_embedding
        self.metadata = metadata or {}
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary."""
        return {
            'speaker_id': self.speaker_id,
            'speaker_name': self.speaker_name,
            'voice_embedding': self.voice_embedding.tolist(),
            'metadata': self.metadata,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VoiceProfile':
        """Create profile from dictionary."""
        profile = cls(
            speaker_id=data['speaker_id'],
            speaker_name=data['speaker_name'],
            voice_embedding=np.array(data['voice_embedding']),
            metadata=data.get('metadata', {})
        )
        profile.created_at = data.get('created_at', profile.created_at)
        profile.updated_at = data.get('updated_at', profile.updated_at)
        return profile
    
    def update_embedding(self, new_embedding: np.ndarray) -> None:
        """Update voice embedding."""
        self.voice_embedding = new_embedding
        self.updated_at = datetime.now().isoformat()

class VoiceDatabase:
    """Database for managing voice profiles and speaker data."""
    
    def __init__(self, database_path: str = "voice_database"):
        """
        Initialize voice database.
        
        Args:
            database_path: Path to database directory
        """
        self.database_path = database_path
        self.profiles_path = os.path.join(database_path, "profiles")
        self.metadata_path = os.path.join(database_path, "metadata.json")
        
        # Ensure directories exist
        ensure_directory(self.database_path)
        ensure_directory(self.profiles_path)
        
        # Load or initialize metadata
        self.metadata = self._load_metadata()
        self.profiles: Dict[int, VoiceProfile] = {}
        
        # Load existing profiles
        self._load_profiles()
        
        logger.info(f"VoiceDatabase initialized at {database_path}")
        logger.info(f"Loaded {len(self.profiles)} voice profiles")
    
    def register_speaker(self, 
                        speaker_name: str,
                        voice_embedding: np.ndarray,
                        metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Register a new speaker.
        
        Args:
            speaker_name: Name of the speaker
            voice_embedding: Voice embedding vector
            metadata: Additional metadata
            
        Returns:
            Speaker ID
        """
        # Check if speaker already exists
        existing_id = self.get_speaker_id_by_name(speaker_name)
        if existing_id is not None:
            logger.warning(f"Speaker '{speaker_name}' already exists with ID {existing_id}")
            return existing_id
        
        # Generate new speaker ID
        speaker_id = self._generate_speaker_id()
        
        # Create voice profile
        profile = VoiceProfile(
            speaker_id=speaker_id,
            speaker_name=speaker_name,
            voice_embedding=voice_embedding,
            metadata=metadata
        )
        
        # Add to database
        self.profiles[speaker_id] = profile
        
        # Save profile
        self._save_profile(profile)
        
        # Update metadata
        self.metadata['total_speakers'] = len(self.profiles)
        self.metadata['last_updated'] = datetime.now().isoformat()
        self._save_metadata()
        
        logger.info(f"Registered new speaker: {speaker_name} (ID: {speaker_id})")
        return speaker_id
    
    def update_speaker_embedding(self, 
                                speaker_id: int,
                                new_embedding: np.ndarray) -> bool:
        """
        Update speaker's voice embedding.
        
        Args:
            speaker_id: Speaker ID
            new_embedding: New voice embedding
            
        Returns:
            True if successful, False otherwise
        """
        if speaker_id not in self.profiles:
            logger.error(f"Speaker ID {speaker_id} not found")
            return False
        
        # Update embedding
        self.profiles[speaker_id].update_embedding(new_embedding)
        
        # Save updated profile
        self._save_profile(self.profiles[speaker_id])
        
        # Update metadata
        self.metadata['last_updated'] = datetime.now().isoformat()
        self._save_metadata()
        
        logger.info(f"Updated embedding for speaker ID {speaker_id}")
        return True
    
    def get_speaker_profile(self, speaker_id: int) -> Optional[VoiceProfile]:
        """
        Get speaker profile by ID.
        
        Args:
            speaker_id: Speaker ID
            
        Returns:
            Voice profile or None if not found
        """
        return self.profiles.get(speaker_id)
    
    def get_speaker_id_by_name(self, speaker_name: str) -> Optional[int]:
        """
        Get speaker ID by name.
        
        Args:
            speaker_name: Speaker name
            
        Returns:
            Speaker ID or None if not found
        """
        for speaker_id, profile in self.profiles.items():
            if profile.speaker_name == speaker_name:
                return speaker_id
        return None
    
    def get_all_speakers(self) -> List[VoiceProfile]:
        """Get all speaker profiles."""
        return list(self.profiles.values())
    
    def get_speaker_embeddings(self) -> Tuple[List[int], np.ndarray]:
        """
        Get all speaker embeddings.
        
        Returns:
            Tuple of (speaker_ids, embeddings_matrix)
        """
        if not self.profiles:
            return [], np.array([])
        
        speaker_ids = list(self.profiles.keys())
        embeddings = np.array([
            self.profiles[sid].voice_embedding 
            for sid in speaker_ids
        ])
        
        return speaker_ids, embeddings
    
    def find_similar_speakers(self, 
                             query_embedding: np.ndarray,
                             threshold: float = 0.8,
                             top_k: int = 5) -> List[Tuple[int, str, float]]:
        """
        Find speakers similar to query embedding.
        
        Args:
            query_embedding: Query voice embedding
            threshold: Similarity threshold
            top_k: Number of top results to return
            
        Returns:
            List of (speaker_id, speaker_name, similarity_score)
        """
        if not self.profiles:
            return []
        
        similarities = []
        
        # Normalize query embedding
        query_norm = query_embedding / np.linalg.norm(query_embedding)
        
        for speaker_id, profile in self.profiles.items():
            # Normalize profile embedding
            profile_norm = profile.voice_embedding / np.linalg.norm(profile.voice_embedding)
            
            # Compute cosine similarity
            similarity = np.dot(query_norm, profile_norm)
            
            if similarity >= threshold:
                similarities.append((speaker_id, profile.speaker_name, float(similarity)))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[2], reverse=True)
        
        return similarities[:top_k]
    
    def remove_speaker(self, speaker_id: int) -> bool:
        """
        Remove speaker from database.
        
        Args:
            speaker_id: Speaker ID to remove
            
        Returns:
            True if successful, False otherwise
        """
        if speaker_id not in self.profiles:
            logger.error(f"Speaker ID {speaker_id} not found")
            return False
        
        # Remove profile file
        profile_file = os.path.join(self.profiles_path, f"speaker_{speaker_id}.pkl")
        if os.path.exists(profile_file):
            os.remove(profile_file)
        
        # Remove from memory
        speaker_name = self.profiles[speaker_id].speaker_name
        del self.profiles[speaker_id]
        
        # Update metadata
        self.metadata['total_speakers'] = len(self.profiles)
        self.metadata['last_updated'] = datetime.now().isoformat()
        self._save_metadata()
        
        logger.info(f"Removed speaker: {speaker_name} (ID: {speaker_id})")
        return True
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        return {
            'total_speakers': len(self.profiles),
            'database_path': self.database_path,
            'created_at': self.metadata.get('created_at'),
            'last_updated': self.metadata.get('last_updated'),
            'speaker_names': [p.speaker_name for p in self.profiles.values()]
        }
    
    def _generate_speaker_id(self) -> int:
        """Generate unique speaker ID."""
        if not self.profiles:
            return 1
        return max(self.profiles.keys()) + 1
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load database metadata."""
        if os.path.exists(self.metadata_path):
            try:
                with open(self.metadata_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading metadata: {e}")
        
        # Default metadata
        return {
            'version': '1.0.0',
            'created_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'total_speakers': 0
        }
    
    def _save_metadata(self) -> None:
        """Save database metadata."""
        try:
            with open(self.metadata_path, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving metadata: {e}")
    
    def _load_profiles(self) -> None:
        """Load all voice profiles."""
        if not os.path.exists(self.profiles_path):
            return
        
        for filename in os.listdir(self.profiles_path):
            if filename.startswith('speaker_') and filename.endswith('.pkl'):
                profile_path = os.path.join(self.profiles_path, filename)
                try:
                    with open(profile_path, 'rb') as f:
                        profile_data = pickle.load(f)
                        profile = VoiceProfile.from_dict(profile_data)
                        self.profiles[profile.speaker_id] = profile
                except Exception as e:
                    logger.error(f"Error loading profile {filename}: {e}")
    
    def _save_profile(self, profile: VoiceProfile) -> None:
        """Save voice profile to disk."""
        profile_path = os.path.join(self.profiles_path, f"speaker_{profile.speaker_id}.pkl")
        try:
            with open(profile_path, 'wb') as f:
                pickle.dump(profile.to_dict(), f)
        except Exception as e:
            logger.error(f"Error saving profile for speaker {profile.speaker_id}: {e}")
