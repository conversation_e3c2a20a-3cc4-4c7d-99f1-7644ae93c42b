"""
Utility functions for VoX-1 Voice ID Engine
"""

import os
import numpy as np
import logging
from typing import Tuple, Optional

def setup_logging(level=logging.INFO):
    """Setup logging configuration."""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def ensure_directory(path: str) -> None:
    """Ensure directory exists, create if it doesn't."""
    os.makedirs(path, exist_ok=True)

def normalize_audio(audio: np.ndarray) -> np.ndarray:
    """
    Normalize audio signal to [-1, 1] range.
    
    Args:
        audio: Audio signal array
        
    Returns:
        Normalized audio signal
    """
    if len(audio) == 0:
        return audio
        
    max_val = np.max(np.abs(audio))
    if max_val > 0:
        return audio / max_val
    return audio

def apply_voice_activity_detection(audio: np.ndarray, sr: int, 
                                 frame_length: int = 2048,
                                 hop_length: int = 512,
                                 energy_threshold: float = 0.01) -> np.ndarray:
    """
    Simple voice activity detection based on energy.
    
    Args:
        audio: Audio signal
        sr: Sample rate
        frame_length: Frame length for analysis
        hop_length: Hop length between frames
        energy_threshold: Energy threshold for voice detection
        
    Returns:
        Audio with silence removed
    """
    # Calculate frame energy
    frame_count = len(audio) // hop_length
    energy = []
    
    for i in range(frame_count):
        start = i * hop_length
        end = min(start + frame_length, len(audio))
        frame = audio[start:end]
        frame_energy = np.sum(frame ** 2) / len(frame)
        energy.append(frame_energy)
    
    energy = np.array(energy)
    
    # Find frames with voice activity
    voice_frames = energy > energy_threshold
    
    if not np.any(voice_frames):
        return audio  # Return original if no voice detected
    
    # Extract audio segments with voice activity
    voice_audio = []
    for i, has_voice in enumerate(voice_frames):
        if has_voice:
            start = i * hop_length
            end = min(start + frame_length, len(audio))
            voice_audio.extend(audio[start:end])
    
    return np.array(voice_audio)

def calculate_snr(signal: np.ndarray, noise: np.ndarray) -> float:
    """
    Calculate Signal-to-Noise Ratio.
    
    Args:
        signal: Clean signal
        noise: Noise signal
        
    Returns:
        SNR in dB
    """
    signal_power = np.mean(signal ** 2)
    noise_power = np.mean(noise ** 2)
    
    if noise_power == 0:
        return float('inf')
    
    snr = 10 * np.log10(signal_power / noise_power)
    return snr

def sliding_window(data: np.ndarray, window_size: int, step_size: int) -> np.ndarray:
    """
    Create sliding windows from data.
    
    Args:
        data: Input data
        window_size: Size of each window
        step_size: Step size between windows
        
    Returns:
        Array of windows
    """
    if len(data) < window_size:
        return np.array([data])
    
    windows = []
    for i in range(0, len(data) - window_size + 1, step_size):
        windows.append(data[i:i + window_size])
    
    return np.array(windows)
