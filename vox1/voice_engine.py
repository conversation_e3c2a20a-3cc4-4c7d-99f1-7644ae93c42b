"""
Main voice engine for VoX-1 Voice ID system
Provides high-level API for voice identification
"""

import numpy as np
import os
import logging
from typing import Tuple, Optional, List, Dict, Any, Union
import pyaudio
import wave
import threading
import time

from .audio_processor import AudioProcessor
from .neural_network import VoiceNet
from .voice_database import VoiceDatabase, VoiceProfile
from .utils import setup_logging

logger = logging.getLogger(__name__)

class VoiceEngine:
    """Main engine for voice identification and verification."""
    
    def __init__(self, 
                 model_path: Optional[str] = None,
                 database_path: Optional[str] = None,
                 sample_rate: int = 16000,
                 confidence_threshold: float = 0.7,
                 similarity_threshold: float = 0.8):
        """
        Initialize VoiceEngine.
        
        Args:
            model_path: Path to trained model (optional)
            database_path: Path to voice database (optional)
            sample_rate: Audio sample rate
            confidence_threshold: Minimum confidence for identification
            similarity_threshold: Minimum similarity for verification
        """
        # Setup logging
        setup_logging()
        
        # Initialize components
        self.audio_processor = AudioProcessor(sample_rate=sample_rate)
        self.neural_network = VoiceNet()
        self.voice_database = VoiceDatabase(database_path or "voice_database")
        
        # Configuration
        self.confidence_threshold = confidence_threshold
        self.similarity_threshold = similarity_threshold
        self.is_recording = False
        self.audio_buffer = []
        
        # Load model if provided
        if model_path and os.path.exists(model_path):
            self.neural_network.load_model(model_path)
            logger.info("Loaded pre-trained model")
        
        logger.info("VoiceEngine initialized successfully")
    
    def register_voice(self, 
                      audio_data: Union[str, np.ndarray],
                      speaker_name: str,
                      metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Register a new voice in the system.
        
        Args:
            audio_data: Audio file path or audio array
            speaker_name: Name of the speaker
            metadata: Additional metadata
            
        Returns:
            Speaker ID
        """
        logger.info(f"Registering voice for speaker: {speaker_name}")
        
        # Load or process audio
        if isinstance(audio_data, str):
            audio, _ = self.audio_processor.load_audio(audio_data)
        else:
            audio = audio_data
        
        # Extract voice features
        features = self.audio_processor.extract_voice_features(audio)
        
        if len(features) == 0:
            raise ValueError("No voice features could be extracted from audio")
        
        # Get voice embedding
        if self.neural_network.encoder is not None:
            embedding = self.neural_network.get_voice_embedding(features)
        else:
            # Use features directly if no trained model
            embedding = features
        
        # Register in database
        speaker_id = self.voice_database.register_speaker(
            speaker_name=speaker_name,
            voice_embedding=embedding,
            metadata=metadata
        )
        
        logger.info(f"Successfully registered speaker: {speaker_name} (ID: {speaker_id})")
        return speaker_id
    
    def identify_voice(self, 
                      audio_data: Union[str, np.ndarray],
                      return_confidence: bool = True) -> Union[Tuple[Optional[int], Optional[str], float], 
                                                              Tuple[Optional[int], Optional[str]]]:
        """
        Identify speaker from audio.
        
        Args:
            audio_data: Audio file path or audio array
            return_confidence: Whether to return confidence score
            
        Returns:
            Tuple of (speaker_id, speaker_name, confidence) or (speaker_id, speaker_name)
        """
        logger.debug("Identifying voice from audio")
        
        # Load or process audio
        if isinstance(audio_data, str):
            audio, _ = self.audio_processor.load_audio(audio_data)
        else:
            audio = audio_data
        
        # Extract voice features
        features = self.audio_processor.extract_voice_features(audio)
        
        if len(features) == 0:
            logger.warning("No voice features could be extracted")
            return (None, None, 0.0) if return_confidence else (None, None)
        
        # Get voice embedding
        if self.neural_network.encoder is not None:
            query_embedding = self.neural_network.get_voice_embedding(features)
        else:
            query_embedding = features
        
        # Find similar speakers
        similar_speakers = self.voice_database.find_similar_speakers(
            query_embedding=query_embedding,
            threshold=self.similarity_threshold,
            top_k=1
        )
        
        if not similar_speakers:
            logger.info("No matching speaker found")
            return (None, None, 0.0) if return_confidence else (None, None)
        
        # Get best match
        speaker_id, speaker_name, similarity = similar_speakers[0]
        
        # Check confidence threshold
        if similarity < self.confidence_threshold:
            logger.info(f"Similarity {similarity:.3f} below threshold {self.confidence_threshold}")
            return (None, None, similarity) if return_confidence else (None, None)
        
        logger.info(f"Identified speaker: {speaker_name} (ID: {speaker_id}, confidence: {similarity:.3f})")
        
        if return_confidence:
            return speaker_id, speaker_name, similarity
        else:
            return speaker_id, speaker_name
    
    def verify_voice(self, 
                    audio_data: Union[str, np.ndarray],
                    speaker_id: int) -> Tuple[bool, float]:
        """
        Verify if audio matches a specific speaker.
        
        Args:
            audio_data: Audio file path or audio array
            speaker_id: Speaker ID to verify against
            
        Returns:
            Tuple of (is_match, similarity_score)
        """
        logger.debug(f"Verifying voice against speaker ID: {speaker_id}")
        
        # Get speaker profile
        profile = self.voice_database.get_speaker_profile(speaker_id)
        if profile is None:
            logger.error(f"Speaker ID {speaker_id} not found")
            return False, 0.0
        
        # Load or process audio
        if isinstance(audio_data, str):
            audio, _ = self.audio_processor.load_audio(audio_data)
        else:
            audio = audio_data
        
        # Preprocess audio first
        processed_audio = self.audio_processor.preprocess_audio(audio)

        # Extract voice features
        features = self.audio_processor.extract_voice_features(audio)

        if len(features) == 0:
            logger.warning("No voice features could be extracted")
            return False, 0.0
        
        # Get voice embedding
        if self.neural_network.encoder is not None:
            query_embedding = self.neural_network.get_voice_embedding(features)
            similarity = self.neural_network.compute_similarity(
                query_embedding, profile.voice_embedding
            )
        else:
            # Use proper voice verification without neural network
            similarity = self._compute_voice_similarity(features, profile.voice_embedding, processed_audio)
        
        is_match = similarity >= self.similarity_threshold
        
        logger.info(f"Verification result: {is_match} (similarity: {similarity:.3f})")
        return is_match, float(similarity)
    
    def record_audio(self, duration: float = 5.0, 
                    chunk_size: int = 1024) -> np.ndarray:
        """
        Record audio from microphone.
        
        Args:
            duration: Recording duration in seconds
            chunk_size: Audio chunk size
            
        Returns:
            Recorded audio array
        """
        logger.info(f"Recording audio for {duration} seconds...")
        
        # Initialize PyAudio
        p = pyaudio.PyAudio()
        
        try:
            # Open stream
            stream = p.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.audio_processor.sample_rate,
                input=True,
                frames_per_buffer=chunk_size
            )
            
            # Record audio
            frames = []
            for _ in range(int(self.audio_processor.sample_rate / chunk_size * duration)):
                data = stream.read(chunk_size)
                frames.append(np.frombuffer(data, dtype=np.float32))
            
            # Close stream
            stream.stop_stream()
            stream.close()
            
            # Combine frames
            audio = np.concatenate(frames)
            
            logger.info("Audio recording completed")
            return audio
            
        except Exception as e:
            logger.error(f"Error recording audio: {e}")
            raise
        finally:
            p.terminate()
    
    def start_continuous_identification(self, 
                                      callback_func: callable,
                                      chunk_duration: float = 2.0) -> None:
        """
        Start continuous voice identification.
        
        Args:
            callback_func: Function to call with identification results
            chunk_duration: Duration of each audio chunk
        """
        logger.info("Starting continuous voice identification...")
        
        def identification_loop():
            while self.is_recording:
                try:
                    # Record audio chunk
                    audio = self.record_audio(duration=chunk_duration)
                    
                    # Identify voice
                    result = self.identify_voice(audio, return_confidence=True)
                    
                    # Call callback
                    callback_func(result)
                    
                except Exception as e:
                    logger.error(f"Error in continuous identification: {e}")
                    time.sleep(0.1)
        
        # Start recording
        self.is_recording = True
        self.identification_thread = threading.Thread(target=identification_loop)
        self.identification_thread.daemon = True
        self.identification_thread.start()
    
    def stop_continuous_identification(self) -> None:
        """Stop continuous voice identification."""
        logger.info("Stopping continuous voice identification...")
        self.is_recording = False
        
        if hasattr(self, 'identification_thread'):
            self.identification_thread.join(timeout=5.0)
    
    def train_model(self, 
                   training_data: List[Tuple[np.ndarray, int]],
                   validation_split: float = 0.2,
                   epochs: int = 100,
                   batch_size: int = 32) -> None:
        """
        Train the voice identification model.
        
        Args:
            training_data: List of (features, speaker_id) tuples
            validation_split: Validation data split ratio
            epochs: Number of training epochs
            batch_size: Training batch size
        """
        logger.info(f"Training model with {len(training_data)} samples...")
        
        # Prepare training data
        X = np.array([data[0] for data in training_data])
        y = np.array([data[1] for data in training_data])
        
        # Split data
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # Train model
        history = self.neural_network.train(
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val,
            epochs=epochs,
            batch_size=batch_size
        )
        
        logger.info("Model training completed")
        return history
    
    def save_model(self, model_path: str) -> None:
        """Save the trained model."""
        self.neural_network.save_model(model_path)
        logger.info(f"Model saved to {model_path}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get voice database statistics."""
        return self.voice_database.get_database_stats()
    
    def list_speakers(self) -> List[Dict[str, Any]]:
        """List all registered speakers."""
        profiles = self.voice_database.get_all_speakers()
        return [
            {
                'speaker_id': p.speaker_id,
                'speaker_name': p.speaker_name,
                'created_at': p.created_at,
                'metadata': p.metadata
            }
            for p in profiles
        ]

    def _compute_voice_similarity(self, features1: np.ndarray, features2: np.ndarray, audio: np.ndarray) -> float:
        """
        Compute proper voice similarity without neural network.
        Uses multiple voice characteristics for better discrimination.
        """
        if len(features1) == 0 or len(features2) == 0:
            return 0.0

        # Ensure same dimensions
        min_len = min(len(features1), len(features2))
        f1 = features1[:min_len]
        f2 = features2[:min_len]

        # 1. Basic cosine similarity (but weighted down)
        cosine_sim = np.dot(f1, f2) / (np.linalg.norm(f1) * np.linalg.norm(f2))

        # 2. Euclidean distance (converted to similarity)
        euclidean_dist = np.linalg.norm(f1 - f2)
        euclidean_sim = 1.0 / (1.0 + euclidean_dist)

        # 3. Correlation coefficient
        correlation = np.corrcoef(f1, f2)[0, 1]
        if np.isnan(correlation):
            correlation = 0.0

        # 4. Feature variance similarity (voices should have similar complexity)
        var1, var2 = np.var(f1), np.var(f2)
        var_sim = 1.0 - abs(var1 - var2) / (var1 + var2 + 1e-8)

        # 5. Audio length penalty (too short = less reliable)
        length_penalty = min(1.0, len(audio) / (self.audio_processor.sample_rate * 2.0))

        # 6. Feature distribution similarity (check if features follow similar patterns)
        # Split features into chunks and compare distributions
        chunk_size = min_len // 4
        if chunk_size > 0:
            chunk_sims = []
            for i in range(0, min_len - chunk_size, chunk_size):
                chunk1 = f1[i:i+chunk_size]
                chunk2 = f2[i:i+chunk_size]
                chunk_sim = np.dot(chunk1, chunk2) / (np.linalg.norm(chunk1) * np.linalg.norm(chunk2))
                chunk_sims.append(chunk_sim)
            chunk_consistency = np.mean(chunk_sims) if chunk_sims else 0.0
        else:
            chunk_consistency = 0.0

        # Combine all similarities with weights
        # Much more conservative weighting to prevent false positives
        final_similarity = (
            0.25 * cosine_sim +           # Basic similarity
            0.20 * euclidean_sim +        # Distance-based
            0.15 * abs(correlation) +     # Correlation
            0.15 * var_sim +              # Variance similarity
            0.15 * chunk_consistency +    # Pattern consistency
            0.10 * length_penalty         # Audio quality
        )

        # Apply additional penalties for suspicious cases

        # Penalty for too-high similarity (likely a bug)
        if final_similarity > 0.95:
            final_similarity *= 0.7  # Reduce by 30%

        # Penalty for very uniform features (poor audio quality)
        if np.var(f1) < 0.001 or np.var(f2) < 0.001:
            final_similarity *= 0.4  # Major penalty for flat audio

        # Penalty for mismatched feature magnitudes
        mag_ratio = np.mean(np.abs(f1)) / (np.mean(np.abs(f2)) + 1e-8)
        if mag_ratio > 2.0 or mag_ratio < 0.5:
            final_similarity *= 0.6  # Penalty for very different magnitudes

        # Cap the maximum similarity to prevent false confidence
        final_similarity = min(final_similarity, 0.92)  # Max 92% similarity

        return float(max(0.0, final_similarity))
