"""
VoX-1 Voice ID Engine
A neural network-based voice identification system.
"""

from .voice_engine import VoiceEngine
from .audio_processor import AudioProcessor
from .neural_network import VoiceNet
from .voice_database import VoiceDatabase

__version__ = "1.0.0"
__author__ = "VoX-1 Team"

# Main API exports
__all__ = [
    'VoiceEngine',
    'AudioProcessor', 
    'VoiceNet',
    'VoiceDatabase'
]

# Easy-to-use factory function
def create_voice_engine(model_path=None, database_path=None):
    """
    Create a VoiceEngine instance with default settings.
    
    Args:
        model_path (str, optional): Path to saved model
        database_path (str, optional): Path to voice database
        
    Returns:
        VoiceEngine: Configured voice engine instance
    """
    return VoiceEngine(model_path=model_path, database_path=database_path)
