"""
Setup script for VoX-1 Voice ID Engine
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="vox1-voice-id",
    version="1.0.0",
    author="VoX-1 Team",
    author_email="<EMAIL>",
    description="A neural network-based voice identification engine",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/vox1/voice-id-engine",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Multimedia :: Sound/Audio :: Analysis",
    ],
    python_requires=">=3.7",
    install_requires=[
        "numpy>=1.21.0",
        "scipy>=1.7.0",
        "librosa>=0.9.0",
        "tensorflow>=2.8.0",
        "scikit-learn>=1.0.0",
        "soundfile>=0.10.0",
        "pyaudio>=0.2.11",
        "matplotlib>=3.5.0",
        "joblib>=1.1.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
    },
    entry_points={
        "console_scripts": [
            "vox1-register=examples.register_voice:main",
            "vox1-identify=examples.identify_voice:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
