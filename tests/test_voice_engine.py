"""
Unit tests for VoX-1 Voice Engine
"""

import unittest
import numpy as np
import tempfile
import shutil
import os
import sys

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vox1 import create_voice_engine, VoiceEngine
from vox1.audio_processor import AudioProcessor
from vox1.neural_network import VoiceNet
from vox1.voice_database import VoiceDatabase, VoiceProfile

class TestVoiceEngine(unittest.TestCase):
    """Test cases for VoiceEngine class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.engine = VoiceEngine(database_path=self.temp_dir)
        
        # Create sample audio data
        self.sample_rate = 16000
        self.duration = 2.0
        self.sample_audio = self._generate_sample_audio()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _generate_sample_audio(self):
        """Generate sample audio for testing."""
        t = np.linspace(0, self.duration, int(self.sample_rate * self.duration))
        # Generate a simple sine wave with some noise
        audio = np.sin(2 * np.pi * 440 * t) + 0.1 * np.random.randn(len(t))
        return audio.astype(np.float32)
    
    def test_voice_registration(self):
        """Test voice registration functionality."""
        speaker_name = "Test Speaker"
        
        # Register voice
        speaker_id = self.engine.register_voice(
            audio_data=self.sample_audio,
            speaker_name=speaker_name
        )
        
        # Verify registration
        self.assertIsInstance(speaker_id, int)
        self.assertGreater(speaker_id, 0)
        
        # Check database
        profile = self.engine.voice_database.get_speaker_profile(speaker_id)
        self.assertIsNotNone(profile)
        self.assertEqual(profile.speaker_name, speaker_name)
    
    def test_voice_identification(self):
        """Test voice identification functionality."""
        # Register a speaker first
        speaker_name = "Test Speaker"
        speaker_id = self.engine.register_voice(
            audio_data=self.sample_audio,
            speaker_name=speaker_name
        )
        
        # Test identification with same audio
        result = self.engine.identify_voice(
            audio_data=self.sample_audio,
            return_confidence=True
        )
        
        identified_id, identified_name, confidence = result
        
        # Should identify the same speaker
        self.assertEqual(identified_id, speaker_id)
        self.assertEqual(identified_name, speaker_name)
        self.assertGreater(confidence, 0.0)
    
    def test_voice_verification(self):
        """Test voice verification functionality."""
        # Register a speaker
        speaker_name = "Test Speaker"
        speaker_id = self.engine.register_voice(
            audio_data=self.sample_audio,
            speaker_name=speaker_name
        )
        
        # Test verification with same audio
        is_match, similarity = self.engine.verify_voice(
            audio_data=self.sample_audio,
            speaker_id=speaker_id
        )
        
        # Should match
        self.assertTrue(is_match)
        self.assertGreater(similarity, 0.5)
    
    def test_database_stats(self):
        """Test database statistics."""
        # Initially empty
        stats = self.engine.get_database_stats()
        self.assertEqual(stats['total_speakers'], 0)
        
        # Register a speaker
        self.engine.register_voice(
            audio_data=self.sample_audio,
            speaker_name="Test Speaker"
        )
        
        # Check updated stats
        stats = self.engine.get_database_stats()
        self.assertEqual(stats['total_speakers'], 1)
        self.assertIn("Test Speaker", stats['speaker_names'])
    
    def test_list_speakers(self):
        """Test speaker listing functionality."""
        # Initially empty
        speakers = self.engine.list_speakers()
        self.assertEqual(len(speakers), 0)
        
        # Register speakers
        names = ["Speaker 1", "Speaker 2", "Speaker 3"]
        for name in names:
            self.engine.register_voice(
                audio_data=self.sample_audio,
                speaker_name=name
            )
        
        # Check speaker list
        speakers = self.engine.list_speakers()
        self.assertEqual(len(speakers), 3)
        
        speaker_names = [s['speaker_name'] for s in speakers]
        for name in names:
            self.assertIn(name, speaker_names)

class TestAudioProcessor(unittest.TestCase):
    """Test cases for AudioProcessor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = AudioProcessor()
        self.sample_audio = self._generate_sample_audio()
    
    def _generate_sample_audio(self):
        """Generate sample audio for testing."""
        t = np.linspace(0, 2.0, 32000)  # 2 seconds at 16kHz
        audio = np.sin(2 * np.pi * 440 * t) + 0.1 * np.random.randn(len(t))
        return audio.astype(np.float32)
    
    def test_audio_preprocessing(self):
        """Test audio preprocessing."""
        processed = self.processor.preprocess_audio(self.sample_audio)
        
        # Should return processed audio
        self.assertIsInstance(processed, np.ndarray)
        self.assertGreater(len(processed), 0)
    
    def test_mfcc_extraction(self):
        """Test MFCC feature extraction."""
        features = self.processor.extract_mfcc_features(self.sample_audio)
        
        # Should return MFCC features
        self.assertIsInstance(features, np.ndarray)
        self.assertEqual(features.shape[0], 39)  # 13 MFCC + 13 delta + 13 delta2
    
    def test_spectral_features(self):
        """Test spectral feature extraction."""
        features = self.processor.extract_spectral_features(self.sample_audio)
        
        # Should return spectral features
        self.assertIsInstance(features, np.ndarray)
        self.assertEqual(features.shape[0], 4)  # 4 spectral features
    
    def test_voice_features(self):
        """Test comprehensive voice feature extraction."""
        features = self.processor.extract_voice_features(self.sample_audio)
        
        # Should return combined features
        self.assertIsInstance(features, np.ndarray)
        self.assertGreater(len(features), 0)

class TestVoiceNet(unittest.TestCase):
    """Test cases for VoiceNet class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.voice_net = VoiceNet(input_dim=100, embedding_dim=64)
        self.sample_features = np.random.randn(10, 100).astype(np.float32)
        self.sample_labels = np.array([0, 1, 0, 1, 2, 2, 0, 1, 2, 0])
    
    def test_model_building(self):
        """Test neural network model building."""
        num_speakers = 3
        self.voice_net.build_model(num_speakers)
        
        # Should have model and encoder
        self.assertIsNotNone(self.voice_net.model)
        self.assertIsNotNone(self.voice_net.encoder)
    
    def test_embedding_extraction(self):
        """Test voice embedding extraction."""
        # Build model first
        self.voice_net.build_model(3)
        
        # Extract embedding
        embedding = self.voice_net.get_voice_embedding(self.sample_features[0])
        
        # Should return embedding vector
        self.assertIsInstance(embedding, np.ndarray)
        self.assertEqual(len(embedding), 64)  # embedding_dim
    
    def test_similarity_computation(self):
        """Test similarity computation between embeddings."""
        embedding1 = np.random.randn(64)
        embedding2 = np.random.randn(64)
        
        similarity = self.voice_net.compute_similarity(embedding1, embedding2)
        
        # Should return similarity score
        self.assertIsInstance(similarity, float)
        self.assertGreaterEqual(similarity, -1.0)
        self.assertLessEqual(similarity, 1.0)

class TestVoiceDatabase(unittest.TestCase):
    """Test cases for VoiceDatabase class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.database = VoiceDatabase(self.temp_dir)
        self.sample_embedding = np.random.randn(128).astype(np.float32)
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_speaker_registration(self):
        """Test speaker registration."""
        speaker_name = "Test Speaker"
        
        speaker_id = self.database.register_speaker(
            speaker_name=speaker_name,
            voice_embedding=self.sample_embedding
        )
        
        # Should return valid speaker ID
        self.assertIsInstance(speaker_id, int)
        self.assertGreater(speaker_id, 0)
        
        # Should be able to retrieve profile
        profile = self.database.get_speaker_profile(speaker_id)
        self.assertIsNotNone(profile)
        self.assertEqual(profile.speaker_name, speaker_name)
    
    def test_speaker_lookup(self):
        """Test speaker lookup by name."""
        speaker_name = "Test Speaker"
        
        # Register speaker
        speaker_id = self.database.register_speaker(
            speaker_name=speaker_name,
            voice_embedding=self.sample_embedding
        )
        
        # Lookup by name
        found_id = self.database.get_speaker_id_by_name(speaker_name)
        self.assertEqual(found_id, speaker_id)
        
        # Lookup non-existent speaker
        not_found = self.database.get_speaker_id_by_name("Non-existent")
        self.assertIsNone(not_found)
    
    def test_similar_speakers(self):
        """Test finding similar speakers."""
        # Register multiple speakers
        for i in range(3):
            embedding = np.random.randn(128).astype(np.float32)
            self.database.register_speaker(
                speaker_name=f"Speaker {i}",
                voice_embedding=embedding
            )
        
        # Find similar speakers
        query_embedding = np.random.randn(128).astype(np.float32)
        similar = self.database.find_similar_speakers(
            query_embedding=query_embedding,
            threshold=0.0,  # Low threshold to find all
            top_k=5
        )
        
        # Should find speakers
        self.assertIsInstance(similar, list)
        self.assertLessEqual(len(similar), 3)

def run_tests():
    """Run all tests."""
    # Create test suite
    test_classes = [
        TestVoiceEngine,
        TestAudioProcessor,
        TestVoiceNet,
        TestVoiceDatabase
    ]
    
    suite = unittest.TestSuite()
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
