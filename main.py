#!/usr/bin/env python3
"""
VoX-1 Voice ID Engine - Main Demo Script
A neural network-based voice identification system
"""

import sys
import os
from vox1 import create_voice_engine

def main():
    """Main demo function for VoX-1 Voice ID Engine."""
    print("🎤 Welcome to VoX-1 Voice ID Engine!")
    print("=" * 50)
    print("A neural network-based voice identification system")
    print()

    try:
        # Initialize the voice engine
        print("Initializing VoX-1 Voice ID Engine...")
        engine = create_voice_engine()
        print("✅ Engine initialized successfully!")
        print()

        # Show menu
        while True:
            print("Available options:")
            print("1. Register a new voice")
            print("2. Identify a voice")
            print("3. Verify a voice")
            print("4. List registered speakers")
            print("5. Database statistics")
            print("6. Run simple demo")
            print("7. Exit")
            print()

            choice = input("Enter your choice (1-7): ").strip()

            if choice == "1":
                register_voice_demo(engine)
            elif choice == "2":
                identify_voice_demo(engine)
            elif choice == "3":
                verify_voice_demo(engine)
            elif choice == "4":
                list_speakers_demo(engine)
            elif choice == "5":
                database_stats_demo(engine)
            elif choice == "6":
                simple_demo(engine)
            elif choice == "7":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")

            print("\n" + "="*50 + "\n")

    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def register_voice_demo(engine):
    """Demo for voice registration."""
    print("🎙️ Voice Registration Demo")
    print("-" * 30)

    name = input("Enter speaker name: ").strip()
    if not name:
        print("❌ Speaker name cannot be empty.")
        return

    duration = input("Recording duration in seconds (default: 5): ").strip()
    try:
        duration = float(duration) if duration else 5.0
    except ValueError:
        duration = 5.0

    print(f"Recording audio for {duration} seconds...")
    print("Please speak clearly into the microphone...")

    try:
        audio = engine.record_audio(duration=duration)
        speaker_id = engine.register_voice(
            audio_data=audio,
            speaker_name=name,
            metadata={"source": "main_demo"}
        )

        print(f"✅ Successfully registered: {name}")
        print(f"   Speaker ID: {speaker_id}")

    except Exception as e:
        print(f"❌ Registration failed: {e}")

def identify_voice_demo(engine):
    """Demo for voice identification."""
    print("🔍 Voice Identification Demo")
    print("-" * 30)

    # Check if any speakers are registered
    speakers = engine.list_speakers()
    if not speakers:
        print("❌ No speakers registered. Please register speakers first.")
        return

    print(f"Registered speakers: {len(speakers)}")
    for speaker in speakers:
        print(f"   - {speaker['speaker_name']} (ID: {speaker['speaker_id']})")
    print()

    duration = input("Recording duration in seconds (default: 3): ").strip()
    try:
        duration = float(duration) if duration else 3.0
    except ValueError:
        duration = 3.0

    print(f"Recording audio for {duration} seconds...")
    print("Please speak into the microphone...")

    try:
        audio = engine.record_audio(duration=duration)
        speaker_id, speaker_name, confidence = engine.identify_voice(
            audio_data=audio,
            return_confidence=True
        )

        if speaker_id is not None:
            print(f"✅ Voice identified!")
            print(f"   Speaker: {speaker_name}")
            print(f"   Speaker ID: {speaker_id}")
            print(f"   Confidence: {confidence:.3f}")
        else:
            print("❓ Unknown speaker")
            print(f"   Confidence: {confidence:.3f}")

    except Exception as e:
        print(f"❌ Identification failed: {e}")

def verify_voice_demo(engine):
    """Demo for voice verification."""
    print("✅ Voice Verification Demo")
    print("-" * 30)

    # Check if any speakers are registered
    speakers = engine.list_speakers()
    if not speakers:
        print("❌ No speakers registered. Please register speakers first.")
        return

    print("Available speakers:")
    for speaker in speakers:
        print(f"   {speaker['speaker_id']}: {speaker['speaker_name']}")
    print()

    try:
        speaker_id = int(input("Enter speaker ID to verify against: ").strip())
    except ValueError:
        print("❌ Invalid speaker ID.")
        return

    # Check if speaker exists
    profile = engine.voice_database.get_speaker_profile(speaker_id)
    if profile is None:
        print(f"❌ Speaker ID {speaker_id} not found.")
        return

    duration = input("Recording duration in seconds (default: 3): ").strip()
    try:
        duration = float(duration) if duration else 3.0
    except ValueError:
        duration = 3.0

    print(f"Recording audio for {duration} seconds...")
    print(f"Please speak to verify against: {profile.speaker_name}")

    try:
        audio = engine.record_audio(duration=duration)
        is_match, similarity = engine.verify_voice(audio, speaker_id)

        if is_match:
            print(f"✅ Voice verification PASSED")
            print(f"   Speaker: {profile.speaker_name}")
            print(f"   Similarity: {similarity:.3f}")
        else:
            print(f"❌ Voice verification FAILED")
            print(f"   Expected: {profile.speaker_name}")
            print(f"   Similarity: {similarity:.3f}")

    except Exception as e:
        print(f"❌ Verification failed: {e}")

def list_speakers_demo(engine):
    """Demo for listing speakers."""
    print("👥 Registered Speakers")
    print("-" * 30)

    speakers = engine.list_speakers()

    if not speakers:
        print("No speakers registered.")
        return

    print(f"Total speakers: {len(speakers)}")
    print()

    for speaker in speakers:
        print(f"ID: {speaker['speaker_id']}")
        print(f"Name: {speaker['speaker_name']}")
        print(f"Registered: {speaker['created_at']}")
        if speaker['metadata']:
            print(f"Metadata: {speaker['metadata']}")
        print("-" * 20)

def database_stats_demo(engine):
    """Demo for database statistics."""
    print("📊 Database Statistics")
    print("-" * 30)

    stats = engine.get_database_stats()

    print(f"Total speakers: {stats['total_speakers']}")
    print(f"Database path: {stats['database_path']}")
    print(f"Created: {stats.get('created_at', 'Unknown')}")
    print(f"Last updated: {stats.get('last_updated', 'Unknown')}")

    if stats['speaker_names']:
        print(f"Speaker names: {', '.join(stats['speaker_names'])}")

def simple_demo(engine):
    """Simple interactive demo."""
    print("🚀 Simple Interactive Demo")
    print("-" * 30)
    print("This demo will guide you through:")
    print("1. Registering two speakers")
    print("2. Testing voice identification")
    print()

    if not input("Continue? (y/N): ").lower().startswith('y'):
        return

    try:
        # Register first speaker
        print("\n1. Register first speaker")
        print("Please speak for 5 seconds when ready...")
        input("Press Enter to start recording...")

        audio1 = engine.record_audio(duration=5.0)
        speaker1_id = engine.register_voice(
            audio_data=audio1,
            speaker_name="Demo Speaker 1"
        )
        print(f"✅ Registered Demo Speaker 1 (ID: {speaker1_id})")

        # Register second speaker
        print("\n2. Register second speaker")
        print("Please have a different person speak for 5 seconds...")
        input("Press Enter to start recording...")

        audio2 = engine.record_audio(duration=5.0)
        speaker2_id = engine.register_voice(
            audio_data=audio2,
            speaker_name="Demo Speaker 2"
        )
        print(f"✅ Registered Demo Speaker 2 (ID: {speaker2_id})")

        # Test identification
        print("\n3. Test voice identification")
        for i in range(2):
            print(f"\nTest {i+1}/2:")
            print("Please speak for 3 seconds...")
            input("Press Enter to start recording...")

            test_audio = engine.record_audio(duration=3.0)
            speaker_id, speaker_name, confidence = engine.identify_voice(
                test_audio, return_confidence=True
            )

            if speaker_id is not None:
                print(f"🔍 Identified: {speaker_name} (confidence: {confidence:.3f})")
            else:
                print(f"❓ Unknown speaker (confidence: {confidence:.3f})")

        print("\n🎉 Demo completed!")

    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == '__main__':
    main()
