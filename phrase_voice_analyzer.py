#!/usr/bin/env python3
"""
Phrase-Specific Voice Analyzer for VoX-1
Analyzes the specific phrase "Unlock the device or program" for unique voice characteristics
"""

import numpy as np
import librosa
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

class PhraseVoiceAnalyzer:
    """Analyzes voice characteristics specific to the unlock phrase."""
    
    def __init__(self, sample_rate: int = 16000):
        self.sample_rate = sample_rate
        self.target_phrase = "unlock the device or program"
        
        # Expected phoneme timings for the phrase (approximate)
        self.phoneme_segments = {
            'un': (0.0, 0.3),      # "un" sound
            'lock': (0.3, 0.8),    # "lock" sound  
            'the': (0.8, 1.1),     # "the" sound
            'de': (1.1, 1.4),      # "de" sound
            'vice': (1.4, 1.8),    # "vice" sound
            'or': (1.8, 2.1),      # "or" sound
            'pro': (2.1, 2.5),     # "pro" sound
            'gram': (2.5, 3.0),    # "gram" sound
        }
    
    def extract_phrase_voice_signature(self, audio: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Extract voice signature specific to the unlock phrase.
        
        Args:
            audio: Audio of someone saying "unlock the device or program"
            
        Returns:
            Dictionary of voice characteristics
        """
        signature = {}
        
        # 1. Fundamental frequency analysis for each phoneme
        signature['f0_profile'] = self._extract_f0_profile(audio)
        
        # 2. Formant frequencies for vowel sounds
        signature['formant_profile'] = self._extract_formant_profile(audio)
        
        # 3. Voice onset timing (how they start each word)
        signature['onset_timing'] = self._extract_onset_timing(audio)
        
        # 4. Spectral envelope for each segment
        signature['spectral_envelope'] = self._extract_spectral_envelope(audio)
        
        # 5. Voice quality measures
        signature['voice_quality'] = self._extract_voice_quality(audio)
        
        # 6. Rhythm and timing patterns
        signature['rhythm_pattern'] = self._extract_rhythm_pattern(audio)
        
        return signature
    
    def _extract_f0_profile(self, audio: np.ndarray) -> np.ndarray:
        """Extract fundamental frequency profile across the phrase."""
        # Use librosa to extract pitch
        f0, voiced_flag, voiced_probs = librosa.pyin(
            audio, 
            fmin=librosa.note_to_hz('C2'), 
            fmax=librosa.note_to_hz('C7'),
            sr=self.sample_rate
        )
        
        # Remove NaN values and interpolate
        valid_f0 = f0[~np.isnan(f0)]
        if len(valid_f0) == 0:
            return np.zeros(50)  # Return zeros if no pitch detected
        
        # Resample to fixed length for comparison
        if len(valid_f0) > 50:
            indices = np.linspace(0, len(valid_f0)-1, 50).astype(int)
            f0_profile = valid_f0[indices]
        else:
            f0_profile = np.pad(valid_f0, (0, 50-len(valid_f0)), 'constant')
        
        return f0_profile
    
    def _extract_formant_profile(self, audio: np.ndarray) -> np.ndarray:
        """Extract formant frequencies for vowel identification."""
        # Get spectral features that approximate formants
        mfcc = librosa.feature.mfcc(y=audio, sr=self.sample_rate, n_mfcc=13)
        
        # Focus on lower MFCCs which correlate with formants
        formant_features = mfcc[1:6]  # Skip C0, use C1-C5
        
        # Statistical summary of formants across time
        formant_stats = np.concatenate([
            np.mean(formant_features, axis=1),
            np.std(formant_features, axis=1),
            np.max(formant_features, axis=1),
            np.min(formant_features, axis=1)
        ])
        
        return formant_stats
    
    def _extract_onset_timing(self, audio: np.ndarray) -> np.ndarray:
        """Extract voice onset characteristics."""
        # Detect onset times
        onset_frames = librosa.onset.onset_detect(
            y=audio, 
            sr=self.sample_rate,
            units='time'
        )
        
        if len(onset_frames) == 0:
            return np.zeros(10)
        
        # Calculate inter-onset intervals
        if len(onset_frames) > 1:
            intervals = np.diff(onset_frames)
            # Pad or truncate to fixed size
            if len(intervals) >= 10:
                timing_features = intervals[:10]
            else:
                timing_features = np.pad(intervals, (0, 10-len(intervals)), 'constant')
        else:
            timing_features = np.zeros(10)
        
        return timing_features
    
    def _extract_spectral_envelope(self, audio: np.ndarray) -> np.ndarray:
        """Extract spectral envelope characteristics."""
        # Compute mel spectrogram
        mel_spec = librosa.feature.melspectrogram(
            y=audio, 
            sr=self.sample_rate,
            n_mels=20
        )
        
        # Convert to log scale
        log_mel = librosa.power_to_db(mel_spec)
        
        # Statistical summary across time
        spectral_stats = np.concatenate([
            np.mean(log_mel, axis=1),
            np.std(log_mel, axis=1)
        ])
        
        return spectral_stats
    
    def _extract_voice_quality(self, audio: np.ndarray) -> np.ndarray:
        """Extract voice quality measures."""
        # Spectral centroid (brightness)
        centroid = librosa.feature.spectral_centroid(y=audio, sr=self.sample_rate)[0]
        
        # Spectral rolloff (energy distribution)
        rolloff = librosa.feature.spectral_rolloff(y=audio, sr=self.sample_rate)[0]
        
        # Zero crossing rate (roughness)
        zcr = librosa.feature.zero_crossing_rate(audio)[0]
        
        # Spectral bandwidth (timbre)
        bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=self.sample_rate)[0]
        
        # Combine into quality profile
        quality_features = np.concatenate([
            [np.mean(centroid), np.std(centroid)],
            [np.mean(rolloff), np.std(rolloff)],
            [np.mean(zcr), np.std(zcr)],
            [np.mean(bandwidth), np.std(bandwidth)]
        ])
        
        return quality_features
    
    def _extract_rhythm_pattern(self, audio: np.ndarray) -> np.ndarray:
        """Extract rhythm and timing patterns."""
        # Compute tempo and beat tracking
        tempo, beats = librosa.beat.beat_track(y=audio, sr=self.sample_rate)
        
        # Energy envelope
        hop_length = 512
        frame_length = 2048
        energy = []
        for i in range(0, len(audio) - frame_length, hop_length):
            frame = audio[i:i + frame_length]
            energy.append(np.sum(frame ** 2))
        
        energy = np.array(energy)
        
        # Rhythm features
        rhythm_features = np.array([
            tempo,
            np.mean(energy),
            np.std(energy),
            np.max(energy),
            len(beats) / (len(audio) / self.sample_rate)  # beats per second
        ])
        
        return rhythm_features
    
    def compare_voice_signatures(self, sig1: Dict[str, np.ndarray], sig2: Dict[str, np.ndarray]) -> float:
        """
        Compare two voice signatures and return similarity score.
        
        Args:
            sig1: First voice signature
            sig2: Second voice signature
            
        Returns:
            Similarity score between 0 and 1
        """
        similarities = []
        weights = {
            'f0_profile': 0.25,        # Pitch pattern - very distinctive
            'formant_profile': 0.20,   # Vowel characteristics
            'onset_timing': 0.15,      # Speech timing
            'spectral_envelope': 0.15, # Overall voice timbre
            'voice_quality': 0.15,     # Voice quality measures
            'rhythm_pattern': 0.10     # Rhythm and tempo
        }
        
        total_weight = 0
        weighted_similarity = 0
        
        for feature_name, weight in weights.items():
            if feature_name in sig1 and feature_name in sig2:
                f1 = sig1[feature_name]
                f2 = sig2[feature_name]
                
                # Ensure same length
                min_len = min(len(f1), len(f2))
                f1_norm = f1[:min_len]
                f2_norm = f2[:min_len]
                
                if len(f1_norm) > 0 and len(f2_norm) > 0:
                    # Compute similarity (using correlation for better voice matching)
                    if np.std(f1_norm) > 0 and np.std(f2_norm) > 0:
                        correlation = np.corrcoef(f1_norm, f2_norm)[0, 1]
                        if not np.isnan(correlation):
                            similarity = (correlation + 1) / 2  # Convert from [-1,1] to [0,1]
                        else:
                            similarity = 0.0
                    else:
                        similarity = 0.0
                    
                    weighted_similarity += similarity * weight
                    total_weight += weight
        
        if total_weight > 0:
            final_similarity = weighted_similarity / total_weight
        else:
            final_similarity = 0.0
        
        # Apply realistic constraints
        # Real voice matching should rarely exceed 85% even for same person
        final_similarity = min(final_similarity, 0.85)
        
        # Add some noise to prevent perfect scores
        noise = np.random.normal(0, 0.02)  # Small random variation
        final_similarity = max(0.0, min(1.0, final_similarity + noise))
        
        return final_similarity
