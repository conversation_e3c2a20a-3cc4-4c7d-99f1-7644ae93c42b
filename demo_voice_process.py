#!/usr/bin/env python3
"""
VoX-1 Voice ID Engine - Complete Process Demonstration
Shows the complete process of voice registration and neural network processing
"""

import numpy as np
import sys
import os
from vox1 import create_voice_engine

def generate_synthetic_voice_data(duration=5.0, sample_rate=16000, speaker_characteristics=None):
    """
    Generate synthetic voice-like audio data for demonstration.
    This simulates different speakers with unique voice characteristics.
    """
    if speaker_characteristics is None:
        speaker_characteristics = {
            'fundamental_freq': 150,  # Hz
            'formant_freqs': [800, 1200, 2500],  # Formant frequencies
            'noise_level': 0.1,
            'voice_tremor': 5.0  # Hz
        }
    
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Generate fundamental frequency with some variation
    f0 = speaker_characteristics['fundamental_freq']
    f0_variation = f0 * 0.1 * np.sin(2 * np.pi * speaker_characteristics['voice_tremor'] * t)
    
    # Generate voice signal with harmonics and formants
    voice_signal = np.zeros_like(t)
    
    # Add fundamental and harmonics
    for harmonic in range(1, 6):
        freq = f0 * harmonic + f0_variation
        amplitude = 1.0 / harmonic  # Decreasing amplitude for higher harmonics
        voice_signal += amplitude * np.sin(2 * np.pi * freq * t)
    
    # Add formant resonances
    for formant_freq in speaker_characteristics['formant_freqs']:
        formant_signal = 0.3 * np.sin(2 * np.pi * formant_freq * t)
        # Apply envelope to simulate speech patterns
        envelope = np.abs(np.sin(2 * np.pi * 2 * t))  # 2 Hz speech pattern
        voice_signal += formant_signal * envelope
    
    # Add noise
    noise = speaker_characteristics['noise_level'] * np.random.randn(len(t))
    voice_signal += noise
    
    # Normalize
    voice_signal = voice_signal / np.max(np.abs(voice_signal))
    
    return voice_signal.astype(np.float32)

def demonstrate_complete_process():
    """Demonstrate the complete voice registration and identification process."""
    
    print("🎤 VoX-1 Voice ID Engine - Complete Process Demonstration")
    print("=" * 60)
    print()
    
    # Step 1: Initialize the Voice Engine
    print("Step 1: Initializing VoX-1 Voice ID Engine...")
    engine = create_voice_engine()
    print("✅ Engine initialized successfully!")
    print()
    
    # Step 2: Generate synthetic voice data for different speakers
    print("Step 2: Generating synthetic voice data for demonstration...")
    
    # Speaker 1 characteristics (lower pitch, male-like)
    speaker1_chars = {
        'fundamental_freq': 120,
        'formant_freqs': [700, 1100, 2400],
        'noise_level': 0.08,
        'voice_tremor': 4.0
    }
    
    # Speaker 2 characteristics (higher pitch, female-like)
    speaker2_chars = {
        'fundamental_freq': 200,
        'formant_freqs': [900, 1400, 2800],
        'noise_level': 0.06,
        'voice_tremor': 6.0
    }
    
    # Speaker 3 characteristics (mid-range)
    speaker3_chars = {
        'fundamental_freq': 160,
        'formant_freqs': [750, 1250, 2600],
        'noise_level': 0.09,
        'voice_tremor': 5.5
    }
    
    voice1 = generate_synthetic_voice_data(duration=5.0, speaker_characteristics=speaker1_chars)
    voice2 = generate_synthetic_voice_data(duration=5.0, speaker_characteristics=speaker2_chars)
    voice3 = generate_synthetic_voice_data(duration=5.0, speaker_characteristics=speaker3_chars)
    
    print("✅ Generated synthetic voice data for 3 speakers")
    print(f"   - Speaker 1: {len(voice1)} samples, fundamental freq: {speaker1_chars['fundamental_freq']} Hz")
    print(f"   - Speaker 2: {len(voice2)} samples, fundamental freq: {speaker2_chars['fundamental_freq']} Hz")
    print(f"   - Speaker 3: {len(voice3)} samples, fundamental freq: {speaker3_chars['fundamental_freq']} Hz")
    print()
    
    # Step 3: Register voices in the system
    print("Step 3: Registering voices in the VoX-1 system...")
    
    speaker1_id = engine.register_voice(
        audio_data=voice1,
        speaker_name="Alice (Low Pitch)",
        metadata={"demo": True, "pitch": "low", "gender": "female"}
    )
    
    speaker2_id = engine.register_voice(
        audio_data=voice2,
        speaker_name="Bob (High Pitch)",
        metadata={"demo": True, "pitch": "high", "gender": "male"}
    )
    
    speaker3_id = engine.register_voice(
        audio_data=voice3,
        speaker_name="Charlie (Mid Pitch)",
        metadata={"demo": True, "pitch": "mid", "gender": "non-binary"}
    )
    
    print(f"✅ Registered 3 speakers:")
    print(f"   - Alice (ID: {speaker1_id})")
    print(f"   - Bob (ID: {speaker2_id})")
    print(f"   - Charlie (ID: {speaker3_id})")
    print()
    
    # Step 4: Show what happens during voice registration
    print("Step 4: Analyzing the voice registration process...")
    print()
    
    # Let's manually walk through the process for Speaker 1
    print("🔍 Detailed analysis of Alice's voice registration:")
    
    # Audio preprocessing
    processed_audio = engine.audio_processor.preprocess_audio(voice1)
    print(f"   - Original audio length: {len(voice1)} samples")
    print(f"   - Processed audio length: {len(processed_audio)} samples")
    
    # Feature extraction
    voice_features = engine.audio_processor.extract_voice_features(voice1)
    print(f"   - Extracted {len(voice_features)} voice features")
    
    # MFCC features
    mfcc_features = engine.audio_processor.extract_mfcc_features(processed_audio)
    print(f"   - MFCC features shape: {mfcc_features.shape}")
    print(f"     (39 features: 13 MFCC + 13 delta + 13 delta-delta)")
    
    # Spectral features
    spectral_features = engine.audio_processor.extract_spectral_features(processed_audio)
    print(f"   - Spectral features shape: {spectral_features.shape}")
    print(f"     (4 features: centroid, rolloff, zero-crossing rate, bandwidth)")
    
    # Voice embedding (this would be generated by the neural network)
    print(f"   - Final feature vector: {len(voice_features)} dimensions")
    print(f"     (Statistical features: mean, std, min, max of all features)")
    print()
    
    # Step 5: Test voice identification
    print("Step 5: Testing voice identification...")
    
    # Generate test samples (slightly modified versions of original voices)
    test_voice1 = generate_synthetic_voice_data(duration=3.0, speaker_characteristics=speaker1_chars)
    test_voice2 = generate_synthetic_voice_data(duration=3.0, speaker_characteristics=speaker2_chars)
    test_voice3 = generate_synthetic_voice_data(duration=3.0, speaker_characteristics=speaker3_chars)
    
    # Add some noise to make it more realistic
    test_voice1 += 0.05 * np.random.randn(len(test_voice1))
    test_voice2 += 0.05 * np.random.randn(len(test_voice2))
    test_voice3 += 0.05 * np.random.randn(len(test_voice3))
    
    print("🎯 Identification results:")
    
    # Test identification for each speaker
    for i, (test_voice, expected_name) in enumerate([
        (test_voice1, "Alice (Low Pitch)"),
        (test_voice2, "Bob (High Pitch)"),
        (test_voice3, "Charlie (Mid Pitch)")
    ], 1):
        
        speaker_id, speaker_name, confidence = engine.identify_voice(
            test_voice, return_confidence=True
        )
        
        if speaker_id is not None:
            status = "✅ CORRECT" if speaker_name == expected_name else "❌ INCORRECT"
            print(f"   Test {i}: {status}")
            print(f"     Expected: {expected_name}")
            print(f"     Identified: {speaker_name} (confidence: {confidence:.3f})")
        else:
            print(f"   Test {i}: ❓ UNKNOWN (confidence: {confidence:.3f})")
        print()
    
    # Step 6: Voice verification
    print("Step 6: Testing voice verification...")
    
    # Test verification against Alice
    is_match, similarity = engine.verify_voice(test_voice1, speaker1_id)
    print(f"🔐 Verification against Alice:")
    print(f"   - Match: {'✅ YES' if is_match else '❌ NO'}")
    print(f"   - Similarity score: {similarity:.3f}")
    print()
    
    # Test false verification (Bob's voice against Alice's profile)
    is_match, similarity = engine.verify_voice(test_voice2, speaker1_id)
    print(f"🔐 False verification (Bob's voice vs Alice's profile):")
    print(f"   - Match: {'✅ YES' if is_match else '❌ NO'}")
    print(f"   - Similarity score: {similarity:.3f}")
    print()
    
    # Step 7: Show database statistics
    print("Step 7: Database statistics...")
    stats = engine.get_database_stats()
    print(f"📊 Voice Database:")
    print(f"   - Total speakers: {stats['total_speakers']}")
    print(f"   - Database path: {stats['database_path']}")
    print(f"   - Speakers: {', '.join(stats['speaker_names'])}")
    print()
    
    # Step 8: Show neural network architecture (if available)
    print("Step 8: Neural Network Architecture...")
    if engine.neural_network.model is not None:
        print("🧠 Neural Network Model:")
        print(f"   - Input dimension: {engine.neural_network.input_dim}")
        print(f"   - Embedding dimension: {engine.neural_network.embedding_dim}")
        print(f"   - Hidden layers: {engine.neural_network.hidden_dims}")
        print(f"   - Dropout rate: {engine.neural_network.dropout_rate}")
    else:
        print("🧠 Neural Network Model: Not trained yet")
        print("   - The system is currently using direct feature comparison")
        print("   - For better accuracy, train the neural network with more data")
    print()
    
    print("🎉 Complete VoX-1 Voice ID demonstration finished!")
    print()
    print("📝 Summary of the process:")
    print("   1. Audio preprocessing (normalization, voice activity detection)")
    print("   2. Feature extraction (MFCC, spectral features)")
    print("   3. Statistical feature computation (mean, std, min, max)")
    print("   4. Voice embedding generation (neural network or direct features)")
    print("   5. Speaker registration in database")
    print("   6. Voice identification using similarity matching")
    print("   7. Voice verification against specific speaker profiles")
    print()
    print("🔧 For production use:")
    print("   - Train the neural network with real voice data")
    print("   - Use longer audio samples (5+ seconds) for registration")
    print("   - Implement noise reduction and audio enhancement")
    print("   - Add more sophisticated voice activity detection")

if __name__ == "__main__":
    try:
        demonstrate_complete_process()
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
