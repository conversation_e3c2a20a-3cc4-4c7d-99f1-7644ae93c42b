#!/usr/bin/env python3
"""
Example script for identifying a voice with VoX-1 Voice ID Engine
"""

import argparse
import sys
import os

# Add parent directory to path to import vox1
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vox1 import create_voice_engine

def main():
    parser = argparse.ArgumentParser(description="Identify a voice with VoX-1 Voice ID Engine")
    parser.add_argument("--audio", "-a", help="Audio file path (optional, will record if not provided)")
    parser.add_argument("--duration", "-d", type=float, default=3.0, help="Recording duration in seconds")
    parser.add_argument("--database", help="Database path (optional)")
    parser.add_argument("--model", help="Model path (optional)")
    parser.add_argument("--continuous", "-c", action="store_true", help="Continuous identification mode")
    parser.add_argument("--verify", "-v", type=int, help="Verify against specific speaker ID")
    
    args = parser.parse_args()
    
    try:
        # Create voice engine
        print("Initializing VoX-1 Voice ID Engine...")
        engine = create_voice_engine(
            model_path=args.model,
            database_path=args.database
        )
        
        # Show registered speakers
        speakers = engine.list_speakers()
        if not speakers:
            print("❌ No speakers registered in the database.")
            print("   Please register speakers first using register_voice.py")
            sys.exit(1)
        
        print(f"\nRegistered speakers ({len(speakers)}):")
        for speaker in speakers:
            print(f"   ID {speaker['speaker_id']}: {speaker['speaker_name']}")
        
        if args.continuous:
            # Continuous identification mode
            print("\n🎤 Starting continuous voice identification...")
            print("   Speak into the microphone. Press Ctrl+C to stop.")
            
            def identification_callback(result):
                speaker_id, speaker_name, confidence = result
                if speaker_id is not None:
                    print(f"   🔍 Identified: {speaker_name} (ID: {speaker_id}, confidence: {confidence:.3f})")
                else:
                    print("   ❓ Unknown speaker")
            
            try:
                engine.start_continuous_identification(identification_callback)
                
                # Keep running until interrupted
                import time
                while True:
                    time.sleep(1)
                    
            except KeyboardInterrupt:
                print("\n⏹️  Stopping continuous identification...")
                engine.stop_continuous_identification()
        
        else:
            # Single identification
            if args.audio:
                print(f"Using audio file: {args.audio}")
                audio_data = args.audio
            else:
                print(f"Recording audio for {args.duration} seconds...")
                print("Please speak clearly into the microphone...")
                audio_data = engine.record_audio(duration=args.duration)
            
            if args.verify:
                # Voice verification mode
                print(f"Verifying voice against speaker ID: {args.verify}")
                is_match, similarity = engine.verify_voice(audio_data, args.verify)
                
                if is_match:
                    print(f"✅ Voice verification PASSED")
                    print(f"   Similarity score: {similarity:.3f}")
                else:
                    print(f"❌ Voice verification FAILED")
                    print(f"   Similarity score: {similarity:.3f}")
            
            else:
                # Voice identification mode
                print("Identifying voice...")
                speaker_id, speaker_name, confidence = engine.identify_voice(
                    audio_data, return_confidence=True
                )
                
                if speaker_id is not None:
                    print(f"✅ Voice identified!")
                    print(f"   Speaker: {speaker_name}")
                    print(f"   Speaker ID: {speaker_id}")
                    print(f"   Confidence: {confidence:.3f}")
                else:
                    print("❓ Unknown speaker")
                    print(f"   Confidence: {confidence:.3f}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
