#!/usr/bin/env python3
"""
Example script for registering a voice with VoX-1 Voice ID Engine
"""

import argparse
import sys
import os

# Add parent directory to path to import vox1
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vox1 import create_voice_engine

def main():
    parser = argparse.ArgumentParser(description="Register a voice with VoX-1 Voice ID Engine")
    parser.add_argument("--name", "-n", required=True, help="Speaker name")
    parser.add_argument("--audio", "-a", help="Audio file path (optional, will record if not provided)")
    parser.add_argument("--duration", "-d", type=float, default=5.0, help="Recording duration in seconds")
    parser.add_argument("--database", help="Database path (optional)")
    parser.add_argument("--model", help="Model path (optional)")
    
    args = parser.parse_args()
    
    try:
        # Create voice engine
        print("Initializing VoX-1 Voice ID Engine...")
        engine = create_voice_engine(
            model_path=args.model,
            database_path=args.database
        )
        
        # Get audio data
        if args.audio:
            print(f"Using audio file: {args.audio}")
            audio_data = args.audio
        else:
            print(f"Recording audio for {args.duration} seconds...")
            print("Please speak clearly into the microphone...")
            audio_data = engine.record_audio(duration=args.duration)
        
        # Register voice
        print(f"Registering voice for speaker: {args.name}")
        speaker_id = engine.register_voice(
            audio_data=audio_data,
            speaker_name=args.name,
            metadata={"registration_method": "cli"}
        )
        
        print(f"✅ Successfully registered speaker: {args.name}")
        print(f"   Speaker ID: {speaker_id}")
        
        # Show database stats
        stats = engine.get_database_stats()
        print(f"\nDatabase Statistics:")
        print(f"   Total speakers: {stats['total_speakers']}")
        print(f"   Database path: {stats['database_path']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
