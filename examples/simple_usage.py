#!/usr/bin/env python3
"""
Simple usage example for VoX-1 Voice ID Engine
Demonstrates basic voice registration and identification
"""

import sys
import os

# Add parent directory to path to import vox1
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vox1 import create_voice_engine

def main():
    print("🎤 VoX-1 Voice ID Engine - Simple Usage Example")
    print("=" * 50)
    
    try:
        # Create voice engine
        print("1. Initializing VoX-1 Voice ID Engine...")
        engine = create_voice_engine()
        
        # Register first speaker
        print("\n2. Registering first speaker...")
        print("   Please say something for 5 seconds when prompted...")
        input("   Press Enter to start recording for Speaker 1...")
        
        audio1 = engine.record_audio(duration=5.0)
        speaker1_id = engine.register_voice(
            audio_data=audio1,
            speaker_name="Speaker 1",
            metadata={"demo": True}
        )
        print(f"   ✅ Registered Speaker 1 (ID: {speaker1_id})")
        
        # Register second speaker
        print("\n3. Registering second speaker...")
        print("   Please have a different person speak for 5 seconds...")
        input("   Press Enter to start recording for Speaker 2...")
        
        audio2 = engine.record_audio(duration=5.0)
        speaker2_id = engine.register_voice(
            audio_data=audio2,
            speaker_name="Speaker 2",
            metadata={"demo": True}
        )
        print(f"   ✅ Registered Speaker 2 (ID: {speaker2_id})")
        
        # Show database stats
        stats = engine.get_database_stats()
        print(f"\n4. Database Statistics:")
        print(f"   Total speakers: {stats['total_speakers']}")
        print(f"   Speakers: {', '.join(stats['speaker_names'])}")
        
        # Test identification
        print("\n5. Testing voice identification...")
        print("   Please speak for 3 seconds (try different speakers)...")
        
        for i in range(3):
            input(f"   Press Enter for test {i+1}/3...")
            
            test_audio = engine.record_audio(duration=3.0)
            speaker_id, speaker_name, confidence = engine.identify_voice(
                test_audio, return_confidence=True
            )
            
            if speaker_id is not None:
                print(f"   🔍 Test {i+1}: Identified as {speaker_name} (confidence: {confidence:.3f})")
            else:
                print(f"   ❓ Test {i+1}: Unknown speaker (confidence: {confidence:.3f})")
        
        # Test verification
        print("\n6. Testing voice verification...")
        input("   Press Enter to verify against Speaker 1...")
        
        verify_audio = engine.record_audio(duration=3.0)
        is_match, similarity = engine.verify_voice(verify_audio, speaker1_id)
        
        if is_match:
            print(f"   ✅ Verification PASSED (similarity: {similarity:.3f})")
        else:
            print(f"   ❌ Verification FAILED (similarity: {similarity:.3f})")
        
        print("\n🎉 Demo completed successfully!")
        print("\nYou can now use the VoX-1 Voice ID Engine in your own applications!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
