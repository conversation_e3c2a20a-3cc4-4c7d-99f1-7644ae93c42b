#!/usr/bin/env python3
"""
VoX-1 Voice Unlock System
Your approach: Use the phrase "Unlock the device or program" for voice authentication
"""

import sys
import os
import numpy as np
from vox1 import create_voice_engine

class VoiceUnlockSystem:
    """Voice-based authentication system using a specific unlock phrase."""
    
    def __init__(self):
        self.engine = create_voice_engine()
        self.unlock_phrase = "Unlock the device or program"
        self.match_threshold = 0.90  # 90% match as you suggested
        
    def register_user_voice(self):
        """Register a new user's voice with the unlock phrase."""
        print("🎤 VoX-1 Voice Unlock - User Registration")
        print("=" * 50)
        print()
        
        # Show existing users
        speakers = self.engine.list_speakers()
        if speakers:
            print("📋 Existing users:")
            for i, speaker in enumerate(speakers, 1):
                print(f"   {i}. {speaker['speaker_name']}")
            print()
        
        # Get user name
        user_name = input("Enter your name for voice registration: ").strip()
        if not user_name:
            print("❌ Name cannot be empty.")
            return False
        
        print(f"\n🎯 Registration for: {user_name}")
        print(f"📝 You will need to say: '{self.unlock_phrase}'")
        print("💡 Speak clearly and naturally - this will be your voice key!")
        print()
        
        # Record multiple samples for better accuracy
        voice_samples = []
        for attempt in range(3):
            input(f"Press Enter to record sample {attempt + 1}/3...")
            print("🔴 Recording... Say the unlock phrase now!")
            
            try:
                # Record for 4 seconds (enough time for the phrase)
                audio = self.engine.record_audio(duration=4.0)
                voice_samples.append(audio)
                print("✅ Sample recorded successfully!")
                print()
            except Exception as e:
                print(f"❌ Recording failed: {e}")
                return False
        
        # Process and register the voice
        print("🔄 Processing your voice samples...")
        
        # Combine samples or use the best one
        # For now, we'll use the first sample, but you could average them
        primary_sample = voice_samples[0]
        
        # Extract voice features and convert to binary-like numerical data
        voice_features = self.engine.audio_processor.extract_voice_features(primary_sample)
        
        print(f"📊 Voice Analysis Complete:")
        print(f"   - Audio samples: {len(voice_samples)}")
        print(f"   - Feature dimensions: {len(voice_features)}")
        print(f"   - Binary data points: {len(voice_features) * 32} bits")  # 32-bit floats
        
        # Convert features to binary representation for display
        binary_preview = self._features_to_binary_preview(voice_features[:8])  # Show first 8 features
        print(f"   - Sample binary data: {binary_preview}...")
        
        # Register in the system
        speaker_id = self.engine.register_voice(
            audio_data=primary_sample,
            speaker_name=user_name,
            metadata={
                "unlock_phrase": self.unlock_phrase,
                "registration_samples": len(voice_samples),
                "feature_dimensions": len(voice_features)
            }
        )
        
        print(f"✅ Voice registration successful!")
        print(f"   User: {user_name}")
        print(f"   ID: {speaker_id}")
        print(f"   Voice fingerprint stored as numerical data")
        print()
        
        return True
    
    def authenticate_user(self):
        """Authenticate a user using their voice."""
        print("🔐 VoX-1 Voice Unlock - Authentication")
        print("=" * 50)
        print()
        
        # Show available users
        speakers = self.engine.list_speakers()
        if not speakers:
            print("❌ No users registered. Please register first.")
            return False
        
        print("👥 Select your user profile:")
        for i, speaker in enumerate(speakers, 1):
            print(f"   {i}. {speaker['speaker_name']}")
        print()
        
        try:
            choice = int(input("Enter user number: ").strip())
            if choice < 1 or choice > len(speakers):
                print("❌ Invalid user selection.")
                return False
            
            selected_user = speakers[choice - 1]
            user_name = selected_user['speaker_name']
            user_id = selected_user['speaker_id']
            
        except ValueError:
            print("❌ Please enter a valid number.")
            return False
        
        print(f"\n🎯 Authenticating: {user_name}")
        print(f"📝 Say the unlock phrase: '{self.unlock_phrase}'")
        print("🔊 Speak clearly and naturally...")
        print()
        
        input("Press Enter when ready to authenticate...")
        print("🔴 Recording... Say the unlock phrase now!")
        
        try:
            # Record authentication attempt
            auth_audio = self.engine.record_audio(duration=4.0)
            print("✅ Audio captured, analyzing...")
            
            # Extract features and show binary conversion process
            auth_features = self.engine.audio_processor.extract_voice_features(auth_audio)
            
            print(f"🔍 Voice Analysis:")
            print(f"   - Feature dimensions: {len(auth_features)}")
            print(f"   - Converting to binary data...")
            
            # Show binary data preview
            binary_preview = self._features_to_binary_preview(auth_features[:8])
            print(f"   - Binary representation: {binary_preview}...")
            
            # Verify against the selected user
            is_match, similarity = self.engine.verify_voice(auth_audio, user_id)
            
            print(f"\n🧠 Neural Network Analysis:")
            print(f"   - Similarity score: {similarity:.4f}")
            print(f"   - Required threshold: {self.match_threshold:.4f}")
            print(f"   - Match percentage: {similarity * 100:.2f}%")
            
            if similarity >= self.match_threshold:
                print(f"\n🎉 AUTHENTICATION SUCCESSFUL!")
                print(f"   Welcome back, {user_name}!")
                print(f"   Voice match: {similarity * 100:.2f}%")
                return True
            else:
                print(f"\n❌ AUTHENTICATION FAILED")
                print(f"   Voice match: {similarity * 100:.2f}% (below {self.match_threshold * 100:.0f}% threshold)")
                print(f"   Access denied.")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def _features_to_binary_preview(self, features):
        """Convert voice features to binary representation for display."""
        # Convert first few features to binary for demonstration
        binary_parts = []
        for feature in features[:4]:  # Show first 4 features
            # Convert float to 32-bit integer representation
            int_repr = int(feature * 1000000) & 0xFFFFFFFF  # Scale and mask to 32 bits
            binary = format(int_repr, '032b')  # 32-bit binary
            binary_parts.append(binary[:8])  # Show first 8 bits of each
        
        return ' '.join(binary_parts)
    
    def show_voice_data_analysis(self):
        """Show how voice data is converted to binary."""
        print("🔬 Voice-to-Binary Data Analysis")
        print("=" * 50)
        print()
        
        speakers = self.engine.list_speakers()
        if not speakers:
            print("❌ No users registered.")
            return
        
        print("Select a user to analyze their voice data:")
        for i, speaker in enumerate(speakers, 1):
            print(f"   {i}. {speaker['speaker_name']}")
        
        try:
            choice = int(input("\nEnter user number: ").strip())
            if choice < 1 or choice > len(speakers):
                print("❌ Invalid selection.")
                return
            
            selected_user = speakers[choice - 1]
            user_name = selected_user['speaker_name']
            
            print(f"\n📊 Voice Data Analysis for: {user_name}")
            print("=" * 40)
            
            # Get the user's voice profile
            profile = self.engine.voice_database.get_speaker_profile(selected_user['speaker_id'])
            if profile:
                voice_embedding = profile.voice_embedding
                
                print(f"🎵 Voice Characteristics:")
                print(f"   - Total data points: {len(voice_embedding)}")
                print(f"   - Data size: {len(voice_embedding) * 4} bytes ({len(voice_embedding) * 32} bits)")
                print(f"   - Embedding dimensions: {len(voice_embedding)}D vector space")
                
                print(f"\n🔢 Sample numerical data (first 10 values):")
                for i, value in enumerate(voice_embedding[:10]):
                    binary_repr = format(int(abs(value) * 1000000) & 0xFFFF, '016b')
                    print(f"   [{i:2d}]: {value:8.4f} → {binary_repr}")
                
                print(f"\n💾 How this works:")
                print(f"   1. Your voice saying '{self.unlock_phrase}' is recorded")
                print(f"   2. Audio is converted to {len(voice_embedding)} numerical features")
                print(f"   3. Each feature represents voice characteristics (pitch, formants, etc.)")
                print(f"   4. Neural network creates a unique {len(voice_embedding)}D 'voice fingerprint'")
                print(f"   5. Future attempts are compared against this fingerprint")
                print(f"   6. Match threshold: {self.match_threshold * 100:.0f}% similarity required")
                
        except ValueError:
            print("❌ Please enter a valid number.")
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main program loop."""
    unlock_system = VoiceUnlockSystem()
    
    while True:
        print("\n🎤 VoX-1 Voice Unlock System")
        print("=" * 40)
        print("Your approach: 'Unlock the device or program'")
        print()
        print("Options:")
        print("1. Register new user voice")
        print("2. Authenticate user")
        print("3. Analyze voice data (binary conversion)")
        print("4. Exit")
        print()
        
        choice = input("Enter your choice (1-4): ").strip()
        
        if choice == "1":
            unlock_system.register_user_voice()
        elif choice == "2":
            success = unlock_system.authenticate_user()
            if success:
                print("\n🔓 Device/Program UNLOCKED!")
                print("You now have access to the system.")
            else:
                print("\n🔒 Device/Program remains LOCKED.")
        elif choice == "3":
            unlock_system.show_voice_data_analysis()
        elif choice == "4":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ System error: {e}")
        sys.exit(1)
